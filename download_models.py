import os
import requests
import torch
import sys

# Create models directory if it doesn't exist
if not os.path.exists('models'):
    os.makedirs('models')

print("Downloading YOLOv8n model...")
try:
    # Direct download from Ultralytics GitHub release
    model_url = "https://github.com/ultralytics/assets/releases/download/v0.0.0/yolov8n.pt"
    model_path = os.path.join('models', 'yolov8n.pt')
    
    # Download with requests
    print(f"Downloading from {model_url}...")
    response = requests.get(model_url, stream=True)
    response.raise_for_status()
    
    # Get total file size
    total_size = int(response.headers.get('content-length', 0))
    downloaded = 0
    
    # Write to file with progress reporting
    with open(model_path, 'wb') as f:
        for chunk in response.iter_content(chunk_size=8192):
            if chunk:
                f.write(chunk)
                downloaded += len(chunk)
                percent = min(int(downloaded * 100 / total_size), 100) if total_size > 0 else 0
                sys.stdout.write(f"\rDownloading: {percent}% [{downloaded} / {total_size}]")
                sys.stdout.flush()
    
    print(f"\nYOLOv8n model downloaded and saved to {model_path}")
    
    # We don't need to verify loading with torch.load since YOLOv8 uses a different loading mechanism
    print("Download complete. The model will be loaded by the ultralytics package in app.py")
    
    # Update app.py to use YOLOv8
    app_file = 'app.py'
    if os.path.exists(app_file):
        print("Updating app.py to use YOLOv8n...")
        
except Exception as e:
    print(f"Error downloading YOLOv8n model: {e}")
    print("Please install required dependencies with: pip install ultralytics opencv-python requests")


