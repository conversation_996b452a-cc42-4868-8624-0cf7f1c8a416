<!DOCTYPE html>
<html>
<head>
    <title>{{ camera.name }} - Detection History</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <div class="container">
        <div class="history-header">
            <h1>{{ camera.name }} - Detection History</h1>
            <div class="history-controls">
                <div class="date-selector">
                    <label for="date-picker">Select Date:</label>
                    <input type="date" id="date-picker" value="{{ today }}" max="{{ today }}">
                    <button id="load-date-btn" class="btn">Load</button>
                </div>
                <a href="{{ url_for('index') }}" class="back-btn">Back to Camera List</a>
            </div>
        </div>
        
        <div class="charts-container">
            <div class="chart-card full-width">
                <h2>Minute-by-Minute Presence</h2>
                <div class="chart-wrapper">
                    <canvas id="minuteChart" width="550" height="550"></canvas>
                </div>
            </div>
        </div>
        
        <div class="history-list">
            <h2>Detection Events</h2>
            {% if events %}
                <div class="event-grid">
                    {% for event in events|reverse %}
                        <div class="event-card">
                            <div class="event-time">{{ event.timestamp }}</div>
                            <div class="event-image">
                                <img src="data:image/jpeg;base64,{{ event.image_data|b64encode }}" alt="Detection at {{ event.timestamp }}">
                            </div>
                        </div>
                    {% endfor %}
                </div>
            {% else %}
                <p class="no-events">No detection events recorded yet.</p>
            {% endif %}
        </div>
    </div>

    <script>
        // Parse the data from Flask
        const hourlyData = {{ hourly_data|safe }};
        const minuteData = {{ minute_data|safe }};
        const cameraId = "{{ camera_id }}";
        const today = "{{ today }}";
        let selectedDate = today;
        
        // Function to draw the minute-by-minute chart
        function drawMinuteChart(minuteData, date) {
            const canvas = document.getElementById('minuteChart');
            const ctx = canvas.getContext('2d');
            const width = canvas.width;
            const height = canvas.height;
            const centerX = width / 2;
            const centerY = height / 2 + 30; // Move center down to create more space at top
            const radius = Math.min(centerX, centerY - 50) * 0.75; // Make radius smaller (75% of available space)

            // Clear canvas
            ctx.clearRect(0, 0, width, height);

            // Draw title with more space
            ctx.font = 'bold 16px Arial';
            ctx.fillStyle = '#333';
            ctx.textAlign = 'center';
            ctx.fillText(`Minute-by-Minute Presence - ${date}`, centerX, 50); // Position title higher up

            // Draw clock circle with gradient
            const gradient = ctx.createRadialGradient(centerX, centerY, radius * 0.95, centerX, centerY, radius);
            gradient.addColorStop(0, 'rgba(255, 255, 255, 0.8)');
            gradient.addColorStop(1, 'rgba(240, 240, 240, 0.6)');
            
            ctx.beginPath();
            ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
            ctx.fillStyle = gradient;
            ctx.fill();
            ctx.strokeStyle = '#ccc';
            ctx.lineWidth = 2;
            ctx.stroke();

            // Draw hour markers and labels
            for (let hour = 0; hour < 24; hour++) {
                const angle = (hour / 24) * 2 * Math.PI - Math.PI / 2; // Start at top (12 o'clock)
                
                // Draw hour marker
                const markerOuterX = centerX + Math.cos(angle) * radius;
                const markerOuterY = centerY + Math.sin(angle) * radius;
                const markerInnerX = centerX + Math.cos(angle) * (radius - 15);
                const markerInnerY = centerY + Math.sin(angle) * (radius - 15);
                
                ctx.beginPath();
                ctx.moveTo(markerInnerX, markerInnerY);
                ctx.lineTo(markerOuterX, markerOuterY);
                ctx.strokeStyle = '#999';
                ctx.lineWidth = 2;
                ctx.stroke();
                
                // Draw hour label
                const labelX = centerX + Math.cos(angle) * (radius + 25);
                const labelY = centerY + Math.sin(angle) * (radius + 25);
                
                ctx.font = 'bold 12px Arial';
                ctx.fillStyle = '#555';
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';
                ctx.fillText(`${hour.toString().padStart(2, '0')}:00`, labelX, labelY);
            }

            // Draw minute lines for presence with glow effect
            ctx.shadowColor = 'rgba(255, 165, 0, 0.6)';
            ctx.shadowBlur = 5;
            ctx.lineWidth = 2;
            ctx.strokeStyle = 'rgba(255, 140, 0, 0.7)';

            // Filter minutes with presence
            const presentMinutes = minuteData.filter(item => item.present === 1);

            // Draw lines from center to each minute with presence
            presentMinutes.forEach(item => {
                const [hourStr, minuteStr] = item.time.split(':');
                const hour = parseInt(hourStr);
                const minute = parseInt(minuteStr);
                
                // Calculate angle for this minute
                const minuteOfDay = hour * 60 + minute;
                const angle = (minuteOfDay / (24 * 60)) * 2 * Math.PI - Math.PI / 2; // Start at top
                
                // Draw line from center to edge
                ctx.beginPath();
                ctx.moveTo(centerX, centerY);
                ctx.lineTo(
                    centerX + Math.cos(angle) * radius,
                    centerY + Math.sin(angle) * radius
                );
                ctx.stroke();
            });

            // Reset shadow for center point
            ctx.shadowBlur = 0;

            // Draw center point with gradient
            const centerGradient = ctx.createRadialGradient(centerX, centerY, 0, centerX, centerY, 10);
            centerGradient.addColorStop(0, '#ff8c00');
            centerGradient.addColorStop(1, '#ff4500');
            
            ctx.beginPath();
            ctx.arc(centerX, centerY, 8, 0, 2 * Math.PI);
            ctx.fillStyle = centerGradient;
            ctx.fill();
            ctx.strokeStyle = '#fff';
            ctx.lineWidth = 2;
            ctx.stroke();
        }
        
        // Function to draw the hourly chart
        function drawHourlyChart(hourlyData, date) {
            // Prepare data for hourly chart
            const hourLabels = hourlyData.map(item => item.hour);
            const hourCounts = hourlyData.map(item => item.count);
            
            // Get the canvas element
            const canvas = document.getElementById('hourlyChart');
            const ctx = canvas.getContext('2d');
            
            // Clear any existing chart
            if (window.hourlyChart) {
                window.hourlyChart.destroy();
            }
            
            // Create new chart
            window.hourlyChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: hourLabels,
                    datasets: [{
                        label: 'Maximum People Count',
                        data: hourCounts,
                        backgroundColor: 'rgba(75, 192, 192, 0.2)',
                        borderColor: 'rgb(75, 192, 192)',
                        borderWidth: 2,
                        pointRadius: 3,
                        pointBackgroundColor: 'rgb(75, 192, 192)',
                        tension: 0.1,
                        fill: false
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: `Hourly Maximum Occupancy - ${date}`,
                            font: {
                                size: 16
                            }
                        },
                        legend: {
                            display: true,
                            position: 'top'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'People Count'
                            },
                            ticks: {
                                precision: 0,
                                stepSize: 1
                            },
                            grid: {
                                color: 'rgba(0, 0, 0, 0.1)'
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: 'Hour of Day'
                            },
                            grid: {
                                color: 'rgba(0, 0, 0, 0.1)'
                            }
                        }
                    }
                }
            });
            
            // Log to confirm chart creation
            console.log("Hourly chart created/updated", window.hourlyChart);
        }
        
        // Function to load data for a specific date
        async function loadDateData(date) {
            try {
                // Show loading indicator
                document.getElementById('minuteChart').style.opacity = '0.5';
                
                const response = await fetch(`/get_history_data/${cameraId}?date=${date}`);
                if (!response.ok) {
                    // Handle error silently without alert
                    console.error(`Failed to load data: ${response.status} ${response.statusText}`);
                    document.getElementById('minuteChart').style.opacity = '1';
                    
                    // Still draw the chart with empty data
                    const emptyData = [];
                    for (let hour = 0; hour < 24; hour++) {
                        for (let minute = 0; minute < 60; minute++) {
                            emptyData.push({
                                time: `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`,
                                present: 0
                            });
                        }
                    }
                    drawMinuteChart(emptyData, date);
                    selectedDate = date;
                    return;
                }
                
                const data = await response.json();
                
                // Update chart with the loaded data
                drawMinuteChart(data.minute_data, date);
                
                // Update selected date
                selectedDate = date;
                
                // Restore opacity
                document.getElementById('minuteChart').style.opacity = '1';
                
                // Check if data is empty (silently log, don't alert)
                const hasData = data.minute_data.some(item => item.present > 0);
                if (!hasData) {
                    console.log(`No detection data available for ${date}`);
                } else {
                    console.log(`Loaded historical data for ${date}`);
                }
            } catch (error) {
                // Handle error silently without alert
                console.error('Error loading data:', error);
                document.getElementById('minuteChart').style.opacity = '1';
                
                // Draw empty chart
                const emptyData = [];
                for (let hour = 0; hour < 24; hour++) {
                    for (let minute = 0; minute < 60; minute++) {
                        emptyData.push({
                            time: `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`,
                            present: 0
                        });
                    }
                }
                drawMinuteChart(emptyData, date);
                selectedDate = date;
            }
        }
        
        // Initialize charts with current data
        document.addEventListener('DOMContentLoaded', function() {
            console.log("DOM loaded, initializing chart");
            
            // Initialize minute chart
            drawMinuteChart(minuteData, today);
            
            // Set up date picker event listener
            document.getElementById('load-date-btn').addEventListener('click', function() {
                const datePicker = document.getElementById('date-picker');
                const selectedDate = datePicker.value;
                if (selectedDate) {
                    loadDateData(selectedDate);
                }
            });
            
            // Auto-refresh the page every 30 seconds to show new detections
            // Only auto-refresh if viewing today's data
            let refreshInterval = setInterval(function() {
                if (selectedDate === today) {
                    try {
                        loadDateData(today);
                    } catch (error) {
                        console.error("Auto-refresh error:", error);
                        // Don't show any alerts, just log the error
                    }
                }
            }, 30000);
            
            // Clean up interval when page is unloaded
            window.addEventListener('beforeunload', function() {
                clearInterval(refreshInterval);
            });
        });
    </script>
</body>
</html>










