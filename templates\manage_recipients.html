<!DOCTYPE html>
<html>
<head>
    <title>Manage Notification Recipients</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Manage Notification Recipients</h1>
            <a href="{{ url_for('index') }}" class="back-btn">Back to Dashboard</a>
        </div>
        
        <div class="notification-settings">
            <h2>Notification Settings</h2>
            
            <div class="settings-grid">
                <div class="settings-card">
                    <h3>Digest Cooldown</h3>
                    <p>Set how frequently images are collected for digest emails when motion is detected.</p>
                    <form id="cooldown-form">
                        <div class="form-group">
                            <label for="cooldown-minutes">Cooldown period (minutes):</label>
                            <input type="number" id="cooldown-minutes" name="cooldown_minutes" min="1" max="60" value="{{ cooldown_minutes }}" required>
                            <small class="form-hint">After an image is collected, the system will wait this many minutes before collecting another image from the same camera.</small>
                        </div>
                        <button type="submit" class="btn notification-btn">Save Cooldown Setting</button>
                    </form>
                </div>
                
                <div class="settings-card">
                    <h3>Email Sending Interval</h3>
                    <p>Set how often digest emails are sent.</p>
                    <form id="email-interval-form">
                        <div class="form-group">
                            <label for="interval-hours">Hours:</label>
                            <input type="number" id="interval-hours" name="interval_hours" min="0" max="23" value="{{ interval_hours }}" required>
                            
                            <label for="interval-minutes">Minutes:</label>
                            <input type="number" id="interval-minutes" name="interval_minutes" min="0" max="59" value="{{ interval_minutes }}" required>
                            
                            <small class="form-hint">Digest emails will be sent every {{ interval_hours }} hour(s) and {{ interval_minutes }} minute(s).</small>
                        </div>
                        <button type="submit" class="btn notification-btn">Save Interval Setting</button>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="recipient-management">
            <div class="add-recipient-form">
                <h2>Add New Recipient</h2>
                <form id="add-recipient-form">
                    <div class="form-group">
                        <label for="recipient-name">Name:</label>
                        <input type="text" id="recipient-name" name="name" required>
                    </div>
                    <div class="form-group">
                        <label for="recipient-email">Email Address:</label>
                        <input type="email" id="recipient-email" name="email" required>
                        <small class="form-hint">We'll send notifications to this email address</small>
                    </div>
                    <button type="submit" class="btn notification-btn">Add Recipient</button>
                </form>
            </div>
            
            <div class="recipients-list">
                <h2>Current Recipients</h2>
                <div id="recipients-container">
                    <p id="no-recipients-msg">No recipients configured yet.</p>
                    <ul id="recipients-list"></ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Load recipients when page loads
            loadRecipients();
            
            // Handle cooldown form submission
            document.getElementById('cooldown-form').addEventListener('submit', function(e) {
                e.preventDefault();
                
                const cooldownMinutes = document.getElementById('cooldown-minutes').value;
                
                // Validate input
                if (cooldownMinutes < 1 || cooldownMinutes > 60) {
                    showToast("Cooldown must be between 1 and 60 minutes", "error");
                    return;
                }
                
                // Send data to server
                fetch('/set_digest_cooldown', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        cooldown_minutes: parseInt(cooldownMinutes)
                    }),
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showToast(`Digest cooldown set to ${cooldownMinutes} minute(s)`);
                    } else {
                        showToast(data.message || "Failed to update cooldown", "error");
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showToast("An error occurred while updating the cooldown", "error");
                });
            });
            
            // Handle email interval form submission
            document.getElementById('email-interval-form').addEventListener('submit', function(e) {
                e.preventDefault();
                
                const intervalHours = parseInt(document.getElementById('interval-hours').value);
                const intervalMinutes = parseInt(document.getElementById('interval-minutes').value);
                
                // Validate input
                if (intervalHours < 0 || intervalHours > 23) {
                    showToast("Hours must be between 0 and 23", "error");
                    return;
                }
                
                if (intervalMinutes < 0 || intervalMinutes > 59) {
                    showToast("Minutes must be between 0 and 59", "error");
                    return;
                }
                
                // Ensure at least 1 minute total interval
                if (intervalHours === 0 && intervalMinutes < 1) {
                    showToast("Total interval must be at least 1 minute", "error");
                    return;
                }
                
                // Send data to server
                fetch('/set_email_interval', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        interval_hours: intervalHours,
                        interval_minutes: intervalMinutes
                    }),
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showToast(`Email interval set to ${intervalHours} hour(s) and ${intervalMinutes} minute(s)`);
                    } else {
                        showToast(data.message || "Failed to update email interval", "error");
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showToast("An error occurred while updating the email interval", "error");
                });
            });
            
            // Form submission
            document.getElementById('add-recipient-form').addEventListener('submit', function(e) {
                e.preventDefault();
                
                const name = document.getElementById('recipient-name').value;
                const email = document.getElementById('recipient-email').value;
                
                // Validate email format
                if (!email.includes('@') || !email.includes('.')) {
                    alert("Please enter a valid email address");
                    return;
                }
                
                // Send data to server
                fetch('/add_recipient', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        name: name,
                        email: email
                    }),
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Clear form
                        document.getElementById('recipient-name').value = '';
                        document.getElementById('recipient-email').value = '';
                        
                        // Reload recipients list
                        loadRecipients();
                    } else {
                        alert(data.message || "Failed to add recipient");
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert("An error occurred while adding the recipient");
                });
            });
        });
        
        // Function to load recipients
        function loadRecipients() {
            fetch('/get_recipients')
            .then(response => response.json())
            .then(data => {
                const container = document.getElementById('recipients-container');
                const noRecipientsMsg = document.getElementById('no-recipients-msg');
                
                if (data.recipients && data.recipients.length > 0) {
                    // Hide no recipients message
                    noRecipientsMsg.style.display = 'none';
                    
                    // Clear container
                    container.innerHTML = '';
                    
                    // Create recipient list
                    const list = document.createElement('ul');
                    list.className = 'recipients-list-items';
                    
                    data.recipients.forEach(recipient => {
                        const item = document.createElement('li');
                        item.className = 'recipient-item';
                        
                        const info = document.createElement('div');
                        info.className = 'recipient-info';
                        info.innerHTML = `<strong>${recipient.name}</strong><br>
                                         <span class="email">${recipient.email}</span>`;
                        
                        const actionButtons = document.createElement('div');
                        actionButtons.className = 'recipient-actions';
                        
                        const testBtn = document.createElement('button');
                        testBtn.className = 'test-notification-btn';
                        testBtn.textContent = 'Test Notification';
                        testBtn.onclick = function() {
                            sendTestNotification(recipient.id, recipient.name);
                        };
                        
                        const deleteBtn = document.createElement('button');
                        deleteBtn.className = 'delete-recipient-btn';
                        deleteBtn.textContent = 'Remove';
                        deleteBtn.onclick = function() {
                            deleteRecipient(recipient.id);
                        };
                        
                        actionButtons.appendChild(testBtn);
                        actionButtons.appendChild(deleteBtn);
                        
                        item.appendChild(info);
                        item.appendChild(actionButtons);
                        list.appendChild(item);
                    });
                    
                    container.appendChild(list);
                } else {
                    // Show no recipients message
                    noRecipientsMsg.style.display = 'block';
                    container.innerHTML = '';
                    container.appendChild(noRecipientsMsg);
                }
            })
            .catch(error => {
                console.error('Error:', error);
            });
        }
        
        // Function to send a test notification
        function sendTestNotification(recipientId, recipientName) {
            // Remove the confirmation dialog and just proceed directly
            
            // Show loading indicator
            const loadingMsg = document.createElement('div');
            loadingMsg.id = 'loading-notification';
            loadingMsg.className = 'loading-message';
            loadingMsg.textContent = 'Sending test notification...';
            document.body.appendChild(loadingMsg);
            
            fetch('/send_test_notification', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    recipient_id: recipientId
                }),
            })
            .then(response => response.json())
            .then(data => {
                // Remove loading indicator
                document.body.removeChild(document.getElementById('loading-notification'));
                
                if (data.success) {
                    // Replace alert with a non-blocking notification
                    showToast(`Test notification sent to ${recipientName}`);
                } else {
                    showToast(data.message || "Failed to send test notification", "error");
                }
            })
            .catch(error => {
                // Remove loading indicator
                if (document.getElementById('loading-notification')) {
                    document.body.removeChild(document.getElementById('loading-notification'));
                }
                
                console.error('Error:', error);
                showToast("An error occurred while sending the test notification", "error");
            });
        }
        
        // Function to delete a recipient
        function deleteRecipient(id) {
            // Remove the confirmation dialog and just proceed directly
            fetch(`/delete_recipient/${id}`, {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    loadRecipients();
                    showToast("Recipient removed successfully");
                } else {
                    showToast(data.message || "Failed to delete recipient", "error");
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showToast("An error occurred while deleting the recipient", "error");
            });
        }

        // Add this toast notification function
        function showToast(message, type = "success") {
            const toast = document.createElement('div');
            toast.className = `toast-notification ${type}`;
            toast.textContent = message;
            document.body.appendChild(toast);
            
            // Fade in
            setTimeout(() => {
                toast.style.opacity = '1';
            }, 10);
            
            // Automatically remove after 3 seconds
            setTimeout(() => {
                toast.style.opacity = '0';
                setTimeout(() => {
                    document.body.removeChild(toast);
                }, 300);
            }, 3000);
        }
    </script>
</body>
</html>













